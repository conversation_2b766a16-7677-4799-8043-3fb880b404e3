const express = require('express');
const Role = require('../models/Role');
const { authMiddleware } = require('../middleware/auth');
const { hasPermission } = require('../middleware/permission');
const router = express.Router();

// 获取所有角色 - 需要角色查询权限
router.get('/', authMiddleware, hasPermission('role:query'), async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    const result = await Role.findAll(parseInt(page), parseInt(pageSize));

    res.json({
      code: 200,
      data: result.list,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      message: '获取角色列表成功'
    });
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取角色列表失败'
    });
  }
});

// 根据ID获取角色 - 需要角色查询权限
router.get('/:id', authMiddleware, hasPermission('role:query'), async (req, res) => {
  try {
    const { id } = req.params;
    const role = await Role.findById(id);

    if (!role) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '角色不存在'
      });
    }

    res.json({
      code: 200,
      data: role,
      message: '获取角色信息成功'
    });
  } catch (error) {
    console.error('获取角色信息失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取角色信息失败'
    });
  }
});

// 创建角色 - 需要角色添加权限
router.post('/', authMiddleware, hasPermission('role:add'), async (req, res) => {
  try {
    const { name, code, description, permissions, status } = req.body;

    // 简单验证
    if (!name || !code) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '角色名称和代码都是必填项'
      });
    }

    // 检查角色代码是否已存在
    const existingRole = await Role.findByCode(code);
    if (existingRole) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '角色代码已存在'
      });
    }

    const role = await Role.create({
      name,
      code,
      description,
      permissions,
      status
    });

    res.status(200).json({
      code: 200,
      data: role,
      message: '角色创建成功'
    });
  } catch (error) {
    console.error('创建角色失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '创建角色失败'
    });
  }
});

// 更新角色 - 需要角色更新权限
router.put('/:id', authMiddleware, hasPermission('role:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, permissions, status } = req.body;

    if (!name || !code) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '角色名称和代码都是必填项'
      });
    }

    // 检查角色代码是否已被其他角色使用
    const existingRole = await Role.findByCode(code);
    if (existingRole && existingRole.id !== id) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '角色代码已被其他角色使用'
      });
    }

    const role = await Role.update(id, {
      name,
      code,
      description,
      permissions,
      status
    });

    if (!role) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '角色不存在'
      });
    }

    res.json({
      code: 200,
      data: role,
      message: '角色更新成功'
    });
  } catch (error) {
    console.error('更新角色失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '更新角色失败'
    });
  }
});

// 删除角色 - 需要角色删除权限
router.delete('/:id', authMiddleware, hasPermission('role:delete'), async (req, res) => {
  try {
    const { id } = req.params;
    const deleted = await Role.delete(id);

    if (!deleted) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '角色不存在'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '角色删除成功'
    });
  } catch (error) {
    if (error.message === '该角色正在被用户使用，无法删除') {
      return res.status(400).json({
        code: 400,
        data: null,
        message: error.message
      });
    }

    console.error('删除角色失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '删除角色失败'
    });
  }
});

// 获取用户的角色 - 需要用户查询权限
router.get('/user/:userId', authMiddleware, hasPermission('user:query'), async (req, res) => {
  try {
    const { userId } = req.params;
    const roles = await Role.findByUserId(userId);

    res.json({
      code: 200,
      data: roles,
      message: '获取用户角色成功'
    });
  } catch (error) {
    console.error('获取用户角色失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取用户角色失败'
    });
  }
});

// 为用户分配角色 - 需要用户更新权限
router.post('/assign-user', authMiddleware, hasPermission('user:update'), async (req, res) => {
  try {
    const { userId, roleIds } = req.body;

    if (!userId) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '用户ID是必填项'
      });
    }

    await Role.assignToUser(userId, roleIds || []);

    res.json({
      code: 200,
      data: null,
      message: '角色分配成功'
    });
  } catch (error) {
    console.error('分配角色失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '分配角色失败'
    });
  }
});

module.exports = router;
