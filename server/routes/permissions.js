const express = require('express');
const { authMiddleware } = require('../middleware/auth');
const { hasPermission } = require('../middleware/permission');
const router = express.Router();

// 定义系统所有可用权限
const AVAILABLE_PERMISSIONS = [
  {
    group: 'user',
    groupName: '用户管理',
    permissions: [
      { code: 'user:query', name: '查询用户' },
      { code: 'user:add', name: '新增用户' },
      { code: 'user:update', name: '编辑用户' },
      { code: 'user:delete', name: '删除用户' },
      { code: 'user:manage', name: '用户管理' }
    ]
  },
  {
    group: 'role',
    groupName: '角色管理',
    permissions: [
      { code: 'role:query', name: '查询角色' },
      { code: 'role:add', name: '新增角色' },
      { code: 'role:update', name: '编辑角色' },
      { code: 'role:delete', name: '删除角色' },
      { code: 'role:manage', name: '角色管理' }
    ]
  },
  {
    group: 'menu',
    groupName: '菜单管理',
    permissions: [
      { code: 'menu:query', name: '查询菜单' },
      { code: 'menu:add', name: '新增菜单' },
      { code: 'menu:update', name: '编辑菜单' },
      { code: 'menu:delete', name: '删除菜单' },
      { code: 'menu:manage', name: '菜单管理' }
    ]
  },
  {
    group: 'profile',
    groupName: '个人资料',
    permissions: [
      { code: 'profile:query', name: '查看资料' },
      { code: 'profile:update', name: '编辑资料' }
    ]
  },
  {
    group: 'system',
    groupName: '系统管理',
    permissions: [
      { code: 'system:config', name: '系统配置' },
      { code: 'system:log', name: '系统日志' },
      { code: 'system:backup', name: '数据备份' }
    ]
  }
];

// 获取所有可用权限 - 需要角色管理权限
router.get('/', authMiddleware, hasPermission('role:manage'), async (req, res) => {
  try {
    res.json({
      code: 200,
      data: AVAILABLE_PERMISSIONS,
      message: '获取权限列表成功'
    });
  } catch (error) {
    console.error('获取权限列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取权限列表失败'
    });
  }
});

// 获取权限分组
router.get('/groups', authMiddleware, hasPermission('role:manage'), async (req, res) => {
  try {
    const groups = AVAILABLE_PERMISSIONS.map(group => ({
      code: group.group,
      name: group.groupName
    }));

    res.json({
      code: 200,
      data: groups,
      message: '获取权限分组成功'
    });
  } catch (error) {
    console.error('获取权限分组失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取权限分组失败'
    });
  }
});

module.exports = router;
