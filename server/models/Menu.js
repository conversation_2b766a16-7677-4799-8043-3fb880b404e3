const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Menu {
  // 创建菜单
  static async create(menuData) {
    const { parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark } = menuData;
    const id = uuidv4();

    const sql = `
      INSERT INTO menu (id, parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    try {
      await query(sql, [
        id,
        parent_id || null,
        name,
        title,
        path,
        component,
        icon || '',
        sort_order || 0,
        is_hidden || 0,
        is_cache || 1,
        status || 1,
        remark || ''
      ]);
      return await this.findById(id);
    } catch (error) {
      console.error('创建菜单失败:', error.message);
      throw error;
    }
  }

  // 根据ID查找菜单
  static async findById(id) {
    const sql = 'SELECT * FROM menu WHERE id = ?';

    try {
      const menus = await query(sql, [id]);
      return menus[0] || null;
    } catch (error) {
      console.error('查找菜单失败:', error.message);
      throw error;
    }
  }

  // 获取所有菜单（树形结构）
  static async findAll() {
    const sql = 'SELECT * FROM menu WHERE status = 1 ORDER BY sort_order ASC, create_time ASC';

    try {
      const menus = await query(sql);
      return this.buildMenuTree(menus);
    } catch (error) {
      console.error('获取菜单列表失败:', error.message);
      throw error;
    }
  }

  // 获取用户菜单
  static async findByUserId(userId) {
    const sql = `
      SELECT DISTINCT m.*
      FROM menu m
      INNER JOIN role_menu rm ON m.id = rm.menu_id
      INNER JOIN user_role ur ON rm.role_id = ur.role_id
      WHERE ur.user_id = ? 
        AND m.status = 1
        AND m.is_hidden = 0
      ORDER BY m.sort_order ASC, m.create_time ASC
    `;

    try {
      const menus = await query(sql, [userId]);
      return this.buildMenuTree(menus);
    } catch (error) {
      console.error('获取用户菜单失败:', error.message);
      throw error;
    }
  }

  // 构建菜单树
  static buildMenuTree(menus) {
    const menuMap = new Map();
    const rootMenus = [];

    // 创建菜单映射
    menus.forEach(menu => {
      menuMap.set(menu.id, { ...menu, children: [] });
    });

    // 构建树形结构
    menus.forEach(menu => {
      const menuItem = menuMap.get(menu.id);
      if (menu.parent_id && menuMap.has(menu.parent_id)) {
        // 添加到父菜单的children中
        menuMap.get(menu.parent_id).children.push(menuItem);
      } else {
        // 根菜单
        rootMenus.push(menuItem);
      }
    });

    return rootMenus;
  }

  // 更新菜单
  static async update(id, menuData) {
    const { parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark } = menuData;
    const sql = `
      UPDATE menu
      SET parent_id = ?, name = ?, title = ?, path = ?, component = ?, icon = ?,
          sort_order = ?, is_hidden = ?, is_cache = ?, status = ?, remark = ?
      WHERE id = ?
    `;

    try {
      const result = await query(sql, [parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark, id]);
      if (result.affectedRows === 0) {
        return null;
      }
      return await this.findById(id);
    } catch (error) {
      console.error('更新菜单失败:', error.message);
      throw error;
    }
  }

  // 删除菜单
  static async delete(id) {
    // 检查是否有子菜单
    const childrenSql = 'SELECT COUNT(*) as count FROM menu WHERE parent_id = ?';
    const children = await query(childrenSql, [id]);
    
    if (children[0].count > 0) {
      throw new Error('存在子菜单，无法删除');
    }

    const sql = 'DELETE FROM menu WHERE id = ?';

    try {
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除菜单失败:', error.message);
      throw error;
    }
  }

  // 为角色分配菜单
  static async assignToRole(roleId, menuIds) {
    try {
      await transaction(async (connection) => {
        // 先删除该角色的所有菜单关联
        await connection.execute('DELETE FROM role_menu WHERE role_id = ?', [roleId]);

        // 批量插入新的菜单关联
        if (menuIds && menuIds.length > 0) {
          const values = menuIds.map(menuId => [uuidv4(), roleId, menuId]);
          const placeholders = values.map(() => '(?, ?, ?)').join(', ');
          const flatValues = values.flat();

          await connection.execute(
            `INSERT INTO role_menu (id, role_id, menu_id) VALUES ${placeholders}`,
            flatValues
          );
        }
      });
      return true;
    } catch (error) {
      console.error('分配菜单给角色失败:', error.message);
      throw error;
    }
  }

  // 获取角色的菜单
  static async findByRoleId(roleId) {
    const sql = `
      SELECT m.*
      FROM menu m
      INNER JOIN role_menu rm ON m.id = rm.menu_id
      WHERE rm.role_id = ? AND m.status = 1
      ORDER BY m.sort_order ASC, m.create_time ASC
    `;

    try {
      const menus = await query(sql, [roleId]);
      return this.buildMenuTree(menus);
    } catch (error) {
      console.error('获取角色菜单失败:', error.message);
      throw error;
    }
  }
}

module.exports = Menu;
