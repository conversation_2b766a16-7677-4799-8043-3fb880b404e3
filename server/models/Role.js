const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Role {

  // 创建角色
  static async create(roleData) {
    const { name, code, description, permissions, status } = roleData;
    const id = uuidv4();

    const sql = 'INSERT INTO role (id, name, code, description, permissions, status) VALUES (?, ?, ?, ?, ?, ?)';

    try {
      await query(sql, [id, name, code, description, JSON.stringify(permissions || []), status || 1]);
      return await this.findById(id);
    } catch (error) {
      console.error('创建角色失败:', error.message);
      throw error;
    }
  }

  // 根据ID查找角色
  static async findById(id) {
    const sql = 'SELECT * FROM role WHERE id = ?';

    try {
      const roles = await query(sql, [id]);
      const role = roles[0] || null;
      if (role && role.permissions) {
        role.permissions = JSON.parse(role.permissions);
      }
      return role;
    } catch (error) {
      console.error('查找角色失败:', error.message);
      throw error;
    }
  }

  // 根据代码查找角色
  static async findByCode(code) {
    const sql = 'SELECT * FROM role WHERE code = ?';

    try {
      const roles = await query(sql, [code]);
      const role = roles[0] || null;
      if (role && role.permissions) {
        role.permissions = JSON.parse(role.permissions);
      }
      return role;
    } catch (error) {
      console.error('查找角色失败:', error.message);
      throw error;
    }
  }

  // 获取所有角色
  static async findAll(page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;

    try {
      // 获取总数
      const countSql = 'SELECT COUNT(*) as total FROM role';
      const countResult = await query(countSql);
      const total = countResult[0].total;

      // 获取分页数据
      const sql = 'SELECT * FROM role ORDER BY create_time DESC LIMIT ? OFFSET ?';
      const roles = await query(sql, [pageSize, offset]);



      const processedRoles = roles.map(role => {
        if (role.permissions && typeof role.permissions === 'string') {
          try {
            role.permissions = JSON.parse(role.permissions);
          } catch (e) {
            console.warn('解析角色权限JSON失败:', role.id, role.permissions, e.message);
            role.permissions = [];
          }
        } else if (Array.isArray(role.permissions)) {
          // 如果已经是数组，直接使用
          role.permissions = role.permissions;
        } else {
          role.permissions = [];
        }
        return role;
      });

      return {
        list: processedRoles,
        total: total,
        page: page,
        pageSize: pageSize
      };
    } catch (error) {
      console.error('获取角色列表失败:', error.message);
      throw error;
    }
  }

  // 更新角色
  static async update(id, roleData) {
    const { name, code, description, permissions, status } = roleData;
    const sql = 'UPDATE role SET name = ?, code = ?, description = ?, permissions = ?, status = ? WHERE id = ?';

    try {
      const result = await query(sql, [name, code, description, JSON.stringify(permissions || []), status, id]);
      if (result.affectedRows === 0) {
        return null;
      }
      return await this.findById(id);
    } catch (error) {
      console.error('更新角色失败:', error.message);
      throw error;
    }
  }

  // 删除角色
  static async delete(id) {
    // 检查是否有用户使用该角色
    const userRoleSql = 'SELECT COUNT(*) as count FROM user_role WHERE role_id = ?';
    const userRoles = await query(userRoleSql, [id]);
    
    if (userRoles[0].count > 0) {
      throw new Error('该角色正在被用户使用，无法删除');
    }

    const sql = 'DELETE FROM role WHERE id = ?';

    try {
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除角色失败:', error.message);
      throw error;
    }
  }

  // 获取用户的角色
  static async findByUserId(userId) {
    const sql = `
      SELECT r.*
      FROM role r
      INNER JOIN user_role ur ON r.id = ur.role_id
      WHERE ur.user_id = ? AND r.status = 1
      ORDER BY r.create_time ASC
    `;

    try {
      const roles = await query(sql, [userId]);
      return roles.map(role => {
        if (role.permissions) {
          role.permissions = JSON.parse(role.permissions);
        }
        return role;
      });
    } catch (error) {
      console.error('获取用户角色失败:', error.message);
      throw error;
    }
  }

  // 为用户分配角色
  static async assignToUser(userId, roleIds) {
    try {
      await transaction(async (connection) => {
        // 先删除该用户的所有角色关联
        await connection.execute('DELETE FROM user_role WHERE user_id = ?', [userId]);

        // 批量插入新的角色关联
        if (roleIds && roleIds.length > 0) {
          const values = roleIds.map(roleId => [uuidv4(), userId, roleId]);
          const placeholders = values.map(() => '(?, ?, ?)').join(', ');
          const flatValues = values.flat();
          
          await connection.execute(
            `INSERT INTO user_role (id, user_id, role_id) VALUES ${placeholders}`,
            flatValues
          );
        }
      });
      return true;
    } catch (error) {
      console.error('分配角色给用户失败:', error.message);
      throw error;
    }
  }
}

module.exports = Role;
