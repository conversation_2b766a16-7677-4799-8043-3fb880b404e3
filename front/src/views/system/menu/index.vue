<template>
  <div class="menu-management">
    <!-- 蓝色横幅 - 100%宽度 -->
    <div class="header-banner">
      <div class="container">
        <div class="banner-content">
          <h1 class="page-title">菜单管理</h1>
          <p class="page-description">管理系统菜单结构，配置菜单权限和显示设置</p>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="container">
      <div class="page-content">
        <!-- 操作栏 -->
        <div class="action-bar">
          <t-button theme="primary" @click="handleAdd">
            <template #icon>
              <Plus :size="16" />
            </template>
            新增菜单
          </t-button>
          <t-button variant="outline" @click="handleRefresh">
            <template #icon>
              <RefreshCw :size="16" />
            </template>
            刷新
          </t-button>
        </div>

        <!-- 菜单树表格 -->
        <div class="table-container">
          <t-enhanced-table
            :data="menuData"
            :columns="columns"
            :tree="treeConfig"
            :loading="loading"
            row-key="id"
            :pagination="false"
            stripe
            hover
            expand-on-row-click
          >
          <!-- 菜单名称列 -->
          <template #title="{ row }">
            <span>{{ row.title }}</span>
            <t-tag
              v-if="row.parent_id === null"
              theme="primary"
              variant="light"
              size="small"
              style="margin-left: 8px;"
            >
              父级
            </t-tag>
            <t-tag
              v-else
              theme="success"
              variant="light"
              size="small"
              style="margin-left: 8px;"
            >
              子级
            </t-tag>
          </template>

          <!-- 图标列 -->
          <template #icon="{ row }">
            <component
              v-if="row.icon"
              :is="getIconComponent(row.icon)"
              :size="16"
              class="menu-icon"
            />
            <span v-else>-</span>
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </t-tag>
          </template>

          <!-- 隐藏状态列 -->
          <template #hidden="{ row }">
            <t-tag :theme="row.is_hidden === 0 ? 'success' : 'warning'" variant="light">
              {{ row.is_hidden === 0 ? '显示' : '隐藏' }}
            </t-tag>
          </template>

          <!-- 操作列 -->
          <template #action="{ row }">
            <t-space>
              <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                编辑
              </t-button>
              <t-button theme="success" variant="text" size="small" @click="handleAddChild(row)">
                新增子菜单
              </t-button>
              <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                删除
              </t-button>
            </t-space>
          </template>
        </t-enhanced-table>
      </div>
    </div>
    </div>
  </div>

    <!-- 新增/编辑菜单对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      width="600px"
      :confirm-btn="{ content: '确定', loading: submitLoading }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <t-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="父级菜单" name="parent_id">
          <t-select
            v-model="formData.parent_id"
            placeholder="请选择父级菜单（不选择则为顶级菜单）"
            clearable
          >
            <t-option
              v-for="menu in parentMenuOptions"
              :key="menu.id"
              :value="menu.id"
              :label="menu.title"
            />
          </t-select>
        </t-form-item>

        <t-form-item label="菜单名称" name="name">
          <t-input
            v-model="formData.name"
            placeholder="请输入菜单名称（英文标识）"
          />
        </t-form-item>

        <t-form-item label="菜单标题" name="title">
          <t-input
            v-model="formData.title"
            placeholder="请输入菜单标题（中文显示名称）"
          />
        </t-form-item>

        <t-form-item label="路由路径" name="path">
          <t-input
            v-model="formData.path"
            placeholder="请输入路由路径，如：/system/menu"
          />
        </t-form-item>

        <t-form-item label="组件路径" name="component">
          <t-input
            v-model="formData.component"
            placeholder="请输入组件路径，如：system/menu/index"
          />
        </t-form-item>

        <t-form-item label="菜单图标" name="icon">
          <t-input
            v-model="formData.icon"
            placeholder="请输入图标名称，如：menu"
          />
        </t-form-item>

        <t-form-item label="排序顺序" name="sort_order">
          <t-input-number
            v-model="formData.sort_order"
            :min="0"
            placeholder="请输入排序顺序"
          />
        </t-form-item>

        <t-form-item label="是否隐藏" name="is_hidden">
          <t-radio-group v-model="formData.is_hidden">
            <t-radio :value="0">显示</t-radio>
            <t-radio :value="1">隐藏</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="是否缓存" name="is_cache">
          <t-radio-group v-model="formData.is_cache">
            <t-radio :value="1">缓存</t-radio>
            <t-radio :value="0">不缓存</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio :value="1">启用</t-radio>
            <t-radio :value="0">禁用</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="备注" name="remark">
          <t-textarea
            v-model="formData.remark"
            placeholder="请输入备注信息"
            :maxlength="500"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { Plus, RefreshCw, Menu, Settings, Home, User, Mail } from 'lucide-vue-next'
import { getMenuList, createMenu, updateMenu, deleteMenu } from '@/api'

// 响应式数据
const loading = ref(false)
const menuData = ref([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const editingId = ref(null)

// 表单数据
const formData = reactive({
  parent_id: null,
  name: '',
  title: '',
  path: '',
  component: '',
  icon: '',
  sort_order: 0,
  is_hidden: 0,
  is_cache: 1,
  status: 1,
  remark: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入菜单名称', type: 'error' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '菜单名称只能包含字母、数字和下划线，且以字母开头', type: 'error' }
  ],
  title: [
    { required: true, message: '请输入菜单标题', type: 'error' }
  ],
  path: [
    { required: true, message: '请输入路由路径', type: 'error' },
    { pattern: /^\/[a-zA-Z0-9\/_-]*$/, message: '路由路径必须以/开头，只能包含字母、数字、下划线和连字符', type: 'error' }
  ],
  component: [
    { required: true, message: '请输入组件路径', type: 'error' }
  ]
}

// 表格配置
const treeConfig = {
  childrenKey: 'children',
  treeNodeColumnIndex: 0
}

// 表格列配置
const columns = [
  {
    colKey: 'title',
    title: '菜单名称',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'name',
    title: '标识名称',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'path',
    title: '路由路径',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'component',
    title: '组件路径',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'icon',
    title: '图标',
    width: 100
  },
  {
    colKey: 'sort_order',
    title: '排序',
    width: 80
  },
  {
    colKey: 'status',
    title: '状态',
    width: 80,
    cell: 'status'
  },
  {
    colKey: 'is_hidden',
    title: '显示状态',
    width: 100,
    cell: 'hidden'
  },
  {
    colKey: 'action',
    title: '操作',
    width: 200,
    cell: 'action'
  }
]

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑菜单' : '新增菜单'
})

const parentMenuOptions = computed(() => {
  // 获取所有父级菜单（没有parent_id的菜单）
  return menuData.value.filter(menu => menu.parent_id === null)
})

// 图标组件映射
const iconComponents = {
  menu: Menu,
  setting: Settings,
  home: Home,
  user: User,
  mail: Mail
}

const getIconComponent = (iconName) => {
  return iconComponents[iconName] || Menu
}

// 方法
const loadMenuData = async () => {
  loading.value = true
  try {
    console.log('🔄 开始加载菜单数据...')
    const response = await getMenuList()
    console.log('📊 菜单数据响应:', response)

    if (response.code === 200) {
      menuData.value = response.data || []
      console.log('✅ 菜单数据加载成功:', {
        总数: menuData.value.length,
        菜单项: menuData.value.map(item => ({
          id: item.id,
          title: item.title,
          parent_id: item.parent_id,
          hasChildren: !!(item.children && item.children.length > 0),
          childrenCount: item.children?.length || 0
        }))
      })
    } else {
      console.error('❌ 菜单数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取菜单数据失败')
      menuData.value = []
    }
  } catch (error) {
    console.error('❌ 加载菜单数据异常:', error)
    await MessagePlugin.error('加载菜单数据失败，请检查网络连接')
    menuData.value = []
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  resetForm()
  editingId.value = null
  dialogVisible.value = true
}

const handleAddChild = (row) => {
  resetForm()
  formData.parent_id = row.id
  editingId.value = null
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  Object.assign(formData, row)
  editingId.value = row.id
  dialogVisible.value = true
}

const handleDelete = (row) => {
  // 显示确认对话框
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除菜单"${row.title}"吗？此操作不可恢复。`,
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        console.log('🗑️ 开始删除菜单:', row.id)
        const response = await deleteMenu(row.id)

        if (response.code === 200) {
          await MessagePlugin.success('菜单删除成功')
          await loadMenuData() // 重新加载数据
        } else {
          await MessagePlugin.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('❌ 删除菜单失败:', error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error('删除失败，请稍后重试')
        }
      }
    },
    onCancel: () => {
      console.log('🚫 用户取消删除操作')
    }
  })
}

const handleRefresh = () => {
  loadMenuData()
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  submitLoading.value = true
  try {
    console.log('📝 提交菜单数据:', formData)

    let response
    if (editingId.value) {
      // 编辑菜单
      console.log('✏️ 更新菜单:', editingId.value)
      response = await updateMenu(editingId.value, formData)
    } else {
      // 新增菜单
      console.log('➕ 创建新菜单')
      response = await createMenu(formData)
    }

    if (response.code === 200) {
      const action = editingId.value ? '更新' : '创建'
      await MessagePlugin.success(`菜单${action}成功`)
      dialogVisible.value = false
      await loadMenuData() // 重新加载数据
    } else {
      await MessagePlugin.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('❌ 提交失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('操作失败，请稍后重试')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  // 使用 TDesign 表单的 reset() 方法重置表单
  formRef.value?.reset()
}

// 生命周期
onMounted(() => {
  loadMenuData()
})
</script>

<style lang="less" scoped>
.menu-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-banner {
  width: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  padding: 40px 0;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.banner-content {
  .page-title {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin: 0 0 12px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .page-description {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 18px;
    font-weight: 400;
  }
}

.page-content {
  padding: 0;
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.table-container {
  background: white;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.menu-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .menu-icon {
    color: #6b7280;
  }
}
</style>
