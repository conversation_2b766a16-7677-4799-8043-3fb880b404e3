<template>
  <div class="role-management">
    <!-- 蓝色横幅 - 100%宽度 -->
    <div class="header-banner">
      <div class="container">
        <div class="banner-content">
          <h1 class="page-title">角色管理</h1>
          <p class="page-description">管理系统角色，配置角色权限和菜单分配</p>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="container">
      <div class="page-content">
        <!-- 操作栏 -->
        <div class="action-bar">
          <t-button theme="primary" @click="handleAdd">
            <template #icon>
              <Plus :size="16" />
            </template>
            新增角色
          </t-button>
          <t-button variant="outline" @click="handleRefresh">
            <template #icon>
              <RefreshCw :size="16" />
            </template>
            刷新
          </t-button>
        </div>

        <!-- 角色表格 -->
        <div class="table-container">
          <t-enhanced-table
            :data="roleData"
            :columns="columns"
            :loading="loading"
            row-key="id"
            :pagination="paginationConfig"
            stripe
            hover
            @page-change="handlePageChange"
          >
            <!-- 状态列 -->
            <template #status="{ row }">
              <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </t-tag>
            </template>

            <!-- 权限列 -->
            <template #permissions="{ row }">
              <div v-if="row.permissions && row.permissions.length > 0" class="permissions-display">
                <t-space size="small" class="permissions-preview">
                  <t-tag
                    v-for="permission in getPermissionGroups(row.permissions).slice(0, 2)"
                    :key="permission.group"
                    :theme="getPermissionTheme(permission.group)"
                    variant="light"
                    size="small"
                  >
                    {{ permission.group }}({{ permission.count }})
                  </t-tag>
                  <t-tag
                    v-if="getPermissionGroups(row.permissions).length > 2"
                    theme="default"
                    variant="light"
                    size="small"
                  >
                    +{{ getPermissionGroups(row.permissions).length - 2 }}组
                  </t-tag>
                </t-space>
                <t-button
                  theme="primary"
                  variant="text"
                  size="small"
                  @click="showPermissionDetail(row)"
                  class="view-detail-btn"
                >
                  查看详情
                </t-button>
              </div>
              <span v-else class="text-placeholder">暂无权限</span>
            </template>

            <!-- 操作列 -->
            <template #action="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                  编辑
                </t-button>
                <t-button theme="success" variant="text" size="small" @click="handleAssignMenu(row)">
                  分配菜单
                </t-button>
                <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-enhanced-table>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增/编辑角色对话框 -->
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    width="600px"
    :confirm-btn="{ content: '确定', loading: submitLoading }"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit="handleSubmit"
    >
      <t-form-item label="角色名称" name="name">
        <t-input
          v-model="formData.name"
          placeholder="请输入角色名称"
        />
      </t-form-item>

      <t-form-item label="角色代码" name="code">
        <t-input
          v-model="formData.code"
          placeholder="请输入角色代码（英文标识）"
        />
      </t-form-item>

      <t-form-item label="角色描述" name="description">
        <t-textarea
          v-model="formData.description"
          placeholder="请输入角色描述"
          :maxlength="500"
        />
      </t-form-item>

      <t-form-item label="状态" name="status">
        <t-radio-group v-model="formData.status">
          <t-radio :value="1">启用</t-radio>
          <t-radio :value="0">禁用</t-radio>
        </t-radio-group>
      </t-form-item>
    </t-form>
  </t-dialog>

  <!-- 分配菜单对话框 -->
  <t-dialog
    v-model:visible="menuDialogVisible"
    header="分配菜单"
    width="800px"
    :confirm-btn="{ content: '确定', loading: menuSubmitLoading }"
    @confirm="handleMenuSubmit"
    @cancel="handleMenuCancel"
  >
    <div class="menu-assignment">
      <div class="role-info">
        <h4>为角色 "{{ currentRole?.name }}" 分配菜单</h4>
        <p class="text-placeholder">请选择该角色可以访问的菜单项</p>
      </div>

      <div class="menu-tree-container">
        <t-tree
          ref="menuTreeRef"
          :data="menuTreeData"
          :keys="treeKeys"
          checkable
          expand-all
          :checked="checkedMenuIds"
          @check="handleMenuCheck"
        />
      </div>
    </div>
  </t-dialog>

  <!-- 权限详情对话框 -->
  <t-dialog
    v-model:visible="permissionDetailVisible"
    :header="`角色权限详情 - ${currentPermissionRole?.name}`"
    width="700px"
    :footer="false"
  >
    <div class="permission-detail">
      <div v-for="group in getPermissionGroups(currentPermissionRole?.permissions || [])" :key="group.group" class="permission-group">
        <div class="group-header">
          <t-tag :theme="getPermissionTheme(group.group)" variant="light" size="medium">
            {{ getPermissionGroupName(group.group) }}
          </t-tag>
          <span class="group-count">{{ group.count }}个权限</span>
        </div>
        <div class="group-permissions">
          <t-tag
            v-for="permission in group.permissions"
            :key="permission"
            theme="default"
            variant="outline"
            size="small"
            class="permission-tag"
          >
            {{ getPermissionName(permission) }}
          </t-tag>
        </div>
      </div>
      <div v-if="!currentPermissionRole?.permissions || currentPermissionRole.permissions.length === 0" class="no-permissions">
        <t-empty description="该角色暂无权限" />
      </div>
    </div>
  </t-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { Plus, RefreshCw } from 'lucide-vue-next'
import {
  getRoleList,
  createRole,
  updateRole,
  deleteRole,
  getMenuList,
  getRoleMenus,
  assignMenusToRole
} from '@/api'

// 响应式数据
const loading = ref(false)
const roleData = ref([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const editingId = ref(null)

// 菜单分配相关
const menuDialogVisible = ref(false)
const menuSubmitLoading = ref(false)
const menuTreeRef = ref(null)
const currentRole = ref(null)
const menuTreeData = ref([])
const checkedMenuIds = ref([])

// 权限详情相关
const permissionDetailVisible = ref(false)
const currentPermissionRole = ref(null)

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  description: '',
  permissions: [],
  status: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称', type: 'error' }
  ],
  code: [
    { required: true, message: '请输入角色代码', type: 'error' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '角色代码只能包含字母、数字和下划线，且以字母开头', type: 'error' }
  ]
}

// 表格列配置
const columns = [
  {
    colKey: 'name',
    title: '角色名称',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'code',
    title: '角色代码',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'description',
    title: '角色描述',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'permissions',
    title: '权限',
    width: 200,
    cell: 'permissions'
  },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    cell: 'status'
  },
  {
    colKey: 'create_time',
    title: '创建时间',
    width: 180,
    ellipsis: true
  },
  {
    colKey: 'action',
    title: '操作',
    width: 200,
    cell: 'action'
  }
]

// 树形组件配置
const treeKeys = {
  value: 'id',
  label: 'title',
  children: 'children'
}

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑角色' : '新增角色'
})

// 方法
const loadRoleData = async () => {
  loading.value = true
  try {
    console.log('🔄 开始加载角色数据...')
    const response = await getRoleList({
      page: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    })
    console.log('📊 角色数据响应:', response)

    if (response.code === 200) {
      roleData.value = response.data || []
      paginationConfig.total = response.total || 0
      console.log('✅ 角色数据加载成功:', {
        总数: roleData.value.length,
        角色列表: roleData.value.map(item => ({
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.status
        }))
      })
    } else {
      console.error('❌ 角色数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取角色数据失败')
      roleData.value = []
    }
  } catch (error) {
    console.error('❌ 加载角色数据异常:', error)
    await MessagePlugin.error('加载角色数据失败，请检查网络连接')
    roleData.value = []
  } finally {
    loading.value = false
  }
}

const loadMenuData = async () => {
  try {
    console.log('🔄 开始加载菜单数据...')
    const response = await getMenuList()
    console.log('📊 菜单数据响应:', response)

    if (response.code === 200) {
      menuTreeData.value = response.data || []
      console.log('✅ 菜单数据加载成功')
    } else {
      console.error('❌ 菜单数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取菜单数据失败')
      menuTreeData.value = []
    }
  } catch (error) {
    console.error('❌ 加载菜单数据异常:', error)
    await MessagePlugin.error('加载菜单数据失败，请检查网络连接')
    menuTreeData.value = []
  }
}

const handlePageChange = (pageInfo) => {
  paginationConfig.current = pageInfo.current
  paginationConfig.pageSize = pageInfo.pageSize
  loadRoleData()
}

const handleAdd = () => {
  resetForm()
  editingId.value = null
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  Object.assign(formData, {
    name: row.name,
    code: row.code,
    description: row.description,
    permissions: row.permissions || [],
    status: row.status
  })
  editingId.value = row.id
  dialogVisible.value = true
}

const handleDelete = (row) => {
  // 显示确认对话框
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除角色"${row.name}"吗？此操作不可恢复。`,
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        console.log('🗑️ 开始删除角色:', row.id)
        const response = await deleteRole(row.id)

        if (response.code === 200) {
          await MessagePlugin.success('角色删除成功')
          await loadRoleData() // 重新加载数据
        } else {
          await MessagePlugin.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('❌ 删除角色失败:', error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error('删除失败，请稍后重试')
        }
      }
    },
    onCancel: () => {
      console.log('🚫 用户取消删除操作')
    }
  })
}

const handleRefresh = () => {
  loadRoleData()
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  submitLoading.value = true
  try {
    console.log('📝 提交角色数据:', formData)

    let response
    if (editingId.value) {
      // 编辑角色
      console.log('✏️ 更新角色:', editingId.value)
      response = await updateRole(editingId.value, formData)
    } else {
      // 新增角色
      console.log('➕ 创建新角色')
      response = await createRole(formData)
    }

    if (response.code === 200) {
      const action = editingId.value ? '更新' : '创建'
      await MessagePlugin.success(`角色${action}成功`)
      dialogVisible.value = false
      await loadRoleData() // 重新加载数据
    } else {
      await MessagePlugin.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('❌ 提交失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('操作失败，请稍后重试')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  // 重置表单数据
  Object.assign(formData, {
    name: '',
    code: '',
    description: '',
    permissions: [],
    status: 1
  })
  // 使用 TDesign 表单的 reset() 方法重置表单
  formRef.value?.reset()
}

// 菜单分配相关方法
const handleAssignMenu = async (row) => {
  currentRole.value = row
  menuDialogVisible.value = true

  // 加载菜单数据
  await loadMenuData()

  // 加载角色已有的菜单
  await loadRoleMenus(row.id)
}

const loadRoleMenus = async (roleId) => {
  try {
    console.log('🔄 开始加载角色菜单:', roleId)
    const response = await getRoleMenus(roleId)
    console.log('📊 角色菜单响应:', response)

    if (response.code === 200) {
      // 提取所有菜单ID（包括父级和子级）
      const menuIds = extractMenuIds(response.data || [])
      checkedMenuIds.value = menuIds
      console.log('✅ 角色菜单加载成功:', menuIds)
    } else {
      console.error('❌ 角色菜单加载失败:', response.message)
      checkedMenuIds.value = []
    }
  } catch (error) {
    console.error('❌ 加载角色菜单异常:', error)
    checkedMenuIds.value = []
  }
}

// 递归提取菜单ID
const extractMenuIds = (menus) => {
  const ids = []
  const traverse = (menuList) => {
    menuList.forEach(menu => {
      ids.push(menu.id)
      if (menu.children && menu.children.length > 0) {
        traverse(menu.children)
      }
    })
  }
  traverse(menus)
  return ids
}

const handleMenuCheck = (checkedKeys) => {
  checkedMenuIds.value = checkedKeys
}

const handleMenuSubmit = async () => {
  if (!currentRole.value) return

  menuSubmitLoading.value = true
  try {
    console.log('📝 提交菜单分配:', {
      roleId: currentRole.value.id,
      menuIds: checkedMenuIds.value
    })

    const response = await assignMenusToRole(currentRole.value.id, checkedMenuIds.value)

    if (response.code === 200) {
      await MessagePlugin.success('菜单分配成功')
      menuDialogVisible.value = false
    } else {
      await MessagePlugin.error(response.message || '菜单分配失败')
    }
  } catch (error) {
    console.error('❌ 菜单分配失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('菜单分配失败，请稍后重试')
    }
  } finally {
    menuSubmitLoading.value = false
  }
}

const handleMenuCancel = () => {
  menuDialogVisible.value = false
  currentRole.value = null
  checkedMenuIds.value = []
}

// 权限相关方法
const getPermissionGroups = (permissions) => {
  if (!permissions || !Array.isArray(permissions)) return []

  const groups = {}
  permissions.forEach(permission => {
    const [group] = permission.split(':')
    if (!groups[group]) {
      groups[group] = {
        group,
        permissions: [],
        count: 0
      }
    }
    groups[group].permissions.push(permission)
    groups[group].count++
  })

  return Object.values(groups).sort((a, b) => b.count - a.count)
}

const getPermissionTheme = (group) => {
  const themes = {
    user: 'primary',
    role: 'success',
    menu: 'warning',
    system: 'danger',
    profile: 'default'
  }
  return themes[group] || 'default'
}

const getPermissionGroupName = (group) => {
  const names = {
    user: '用户管理',
    role: '角色管理',
    menu: '菜单管理',
    system: '系统管理',
    profile: '个人资料'
  }
  return names[group] || group
}

const getPermissionName = (permission) => {
  const names = {
    'user:add': '新增用户',
    'user:delete': '删除用户',
    'user:update': '编辑用户',
    'user:query': '查询用户',
    'user:manage': '用户管理',
    'role:add': '新增角色',
    'role:delete': '删除角色',
    'role:update': '编辑角色',
    'role:query': '查询角色',
    'role:manage': '角色管理',
    'menu:add': '新增菜单',
    'menu:delete': '删除菜单',
    'menu:update': '编辑菜单',
    'menu:query': '查询菜单',
    'menu:manage': '菜单管理',
    'profile:query': '查看资料',
    'profile:update': '编辑资料'
  }
  return names[permission] || permission
}

const showPermissionDetail = (role) => {
  currentPermissionRole.value = role
  permissionDetailVisible.value = true
}

// 生命周期
onMounted(() => {
  loadRoleData()
})
</script>

<style lang="less" scoped>
.role-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-banner {
  width: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  padding: 40px 0;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.banner-content {
  .page-title {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin: 0 0 12px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .page-description {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 18px;
    font-weight: 400;
  }
}

.page-content {
  padding: 0;
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.table-container {
  background: white;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.text-placeholder {
  color: #999;
  font-style: italic;
}

// 权限显示样式
.permissions-display {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .permissions-preview {
    flex-wrap: wrap;
  }

  .view-detail-btn {
    align-self: flex-start;
    padding: 0;
    height: auto;
    font-size: 12px;
  }
}

// 权限详情对话框样式
.permission-detail {
  .permission-group {
    margin-bottom: 20px;

    .group-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .group-count {
        color: #666;
        font-size: 12px;
      }
    }

    .group-permissions {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .permission-tag {
        margin-bottom: 4px;
      }
    }
  }

  .no-permissions {
    text-align: center;
    padding: 40px 0;
  }
}

// 菜单分配对话框样式
.menu-assignment {
  .role-info {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .text-placeholder {
      margin: 0;
      font-size: 14px;
    }
  }

  .menu-tree-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    padding: 16px;
    background: white;
  }
}
</style>
