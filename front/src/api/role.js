import { api } from '@/utils/request'

/**
 * 角色管理相关API
 */

// 获取所有角色
export const getRoleList = (params = {}) => {
  return api.get('/api/roles', params)
}

// 根据ID获取角色详情
export const getRoleById = (id) => {
  return api.get(`/api/roles/${id}`)
}

// 创建角色
export const createRole = (data) => {
  return api.post('/api/roles', {
    name: data.name,
    code: data.code,
    description: data.description,
    permissions: data.permissions,
    status: data.status
  })
}

// 更新角色
export const updateRole = (id, data) => {
  return api.put(`/api/roles/${id}`, {
    name: data.name,
    code: data.code,
    description: data.description,
    permissions: data.permissions,
    status: data.status
  })
}

// 删除角色
export const deleteRole = (id) => {
  return api.delete(`/api/roles/${id}`)
}

// 获取用户的角色
export const getUserRoles = (userId) => {
  return api.get(`/api/roles/user/${userId}`)
}

// 为用户分配角色
export const assignRolesToUser = (userId, roleIds) => {
  return api.post('/api/roles/assign-user', {
    userId,
    roleIds
  })
}

// 获取角色的菜单
export const getRoleMenus = (roleId) => {
  return api.get(`/api/menus/role/${roleId}`)
}

// 为角色分配菜单
export const assignMenusToRole = (roleId, menuIds) => {
  return api.post('/api/menus/assign-role', {
    roleId,
    menuIds
  })
}
